﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using Microsoft.Extensions.Logging;
using MvCameraControl;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Threading;

namespace IPM.Vision.BLL
{
    public class HKVisionService : IDisposable
    {
        private readonly NLogHelper _logger;
        readonly DeviceTLayerType enumTLayerType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice
           | DeviceTLayerType.MvGenTLGigEDevice | DeviceTLayerType.MvGenTLCXPDevice | DeviceTLayerType.MvGenTLCameraLinkDevice | DeviceTLayerType.MvGenTLXoFDevice;
        public delegate void HKErrorDelegate(string message);
        public delegate void UpdateGrapping(BitmapSource bitmap);
        public event Action<string> BeforeTakePictureEvent;
        public event UpdateGrapping UpdateGrappingEvent;
        public event HKErrorDelegate HKErrorEvent;
        public event Action CameraConnectedEvent;
        public event Action DisConnectEvent;
        public event Action<PictureModel> UpdatePictureModelEvent;
        private readonly ObservableGlobalState _globalState;
        List<IDeviceInfo> deviceInfoList = new List<IDeviceInfo>();
        private bool _sdkInited = false;
        private string _pictureName = string.Empty;
        private string _pictureFolder = string.Empty;

        private bool _isGrapping = false;
        private readonly object saveImageLock = new object();
        private IDevice _currentDevice;
        private IFrameOut frameForSave;
        public IntPtr RenderHandle { get; set; }
        public int DeviceCount { get => deviceInfoList.Count; }

        /// <summary>
        /// 真正的连接状态：只有设备成功打开并开始采集才为true
        /// </summary>
        public bool IsConnected { get; private set; } = false;

        /// <summary>
        /// 采集状态：是否正在进行图像采集
        /// </summary>
        public bool IsGrabbing => _isGrapping;

        /// <summary>
        /// 快速检查设备连接状态，避免长时间等待
        /// </summary>
        public bool QuickConnectionCheck()
        {
            try
            {
                if (_currentDevice == null) return false;

                // 尝试读取一个简单的参数来验证连接
                IIntValue widthParam;
                var result = _currentDevice.Parameters.GetIntValue("Width", out widthParam);
                return result == MvError.MV_OK && widthParam != null;
            }
            catch
            {
                return false;
            }
        }
        private int _hkIsOK;
        public int HKIsOK
        {
            get => _hkIsOK;
            set
            {
                // **关键修改：不再自动触发错误事件，避免 No data 错误日志**
                _hkIsOK = value;
            }
        }
        public bool SDKInited { get => _sdkInited; }

        // 简化状态控制，移除复杂的锁机制
        private volatile bool _isOperating = false;
        private volatile bool _needReconnect = false;
        private System.Threading.Timer _connectionCheckTimer;
        private DateTime _lastFrameTime = DateTime.Now;
        private const int CONNECTION_CHECK_INTERVAL = 5000; // 5秒检查一次连接状态
        private const int FRAME_TIMEOUT_SECONDS = 30; // 30秒没有帧数据则认为连接异常

        // **性能优化：添加设备缓存，避免重复枚举**
        private static List<IDeviceInfo> _cachedDeviceList = null;
        private static DateTime _lastEnumTime = DateTime.MinValue;
        private static readonly TimeSpan CACHE_VALID_DURATION = TimeSpan.FromMinutes(2); // 缓存2分钟

        public HKVisionService(NLogHelper logger, ObservableGlobalState globalState)
        {
            _logger = logger;
            _globalState = globalState;
            
            // 初始化连接状态监控定时器
            InitializeConnectionMonitoring();
        }

        /// <summary>
        /// 初始化连接状态监控
        /// </summary>
        private void InitializeConnectionMonitoring()
        {
            _connectionCheckTimer = new System.Threading.Timer(MonitorConnection, null, 
                CONNECTION_CHECK_INTERVAL, CONNECTION_CHECK_INTERVAL);
        }

        /// <summary>
        /// 简化的连接监控，不进行自动重连
        /// </summary>
        private void MonitorConnection(object state)
        {
            // **简化：不进行任何监控和重连操作**
            // 让连接保持稳定，不因为数据问题触发重连
        }



        /// <summary>
        /// 使用新的连接方式：直接通过设备型号匹配海康相机
        /// 修复连接不上的问题，使用更直接的连接方式
        /// </summary>
        public void RefreshAndConnect()
        {
            if (_isOperating)
            {
                AddLog("相机操作正在进行中，跳过刷新请求", false);
                return;
            }

            // **关键修复：如果已经连接且正在采集，不要重复连接**
            if (IsConnected && _isGrapping && _currentDevice != null)
            {
                AddLog("海康相机已连接且正在采集，跳过重复连接", false);
                return;
            }

            _isOperating = true;
            _needReconnect = false;

            try
            {
                AddLog("开始使用新的连接方式连接海康相机...");

                // **修改：只有在真正需要时才清理连接**
                if (_currentDevice != null && !IsConnected)
                {
                    CompleteCleanup();
                    Thread.Sleep(1000);
                }

                // **强制修改：忽略配置检查，强制连接海康相机**
                AddLog("🔧 强制连接模式：忽略HaveMarkCamera配置，强制连接海康相机");

                // 4. 初始化SDK（如果需要）
                if (!_sdkInited)
                {
                    if (!InitSDK())
                    {
                        AddLog("SDK初始化失败，无法连接相机", true);
                        return;
                    }
                }

                // **性能优化：使用缓存的设备列表，减少枚举时间**
                deviceInfoList = GetCachedDeviceList();

                if (deviceInfoList.Count == 0)
                {
                    AddLog("未检测到任何相机设备", true);
                    // **新增：提供详细的设备检测失败诊断**
                    AddLog("设备检测失败诊断：");
                    AddLog("1. 检查海康相机是否正确连接到网络");
                    AddLog("2. 确认相机IP地址是否在同一网段");
                    AddLog("3. 检查防火墙是否阻止了相机通信");
                    AddLog("4. 验证海康SDK是否正确安装");
                    AddLog("5. 尝试使用海康官方工具测试相机连接");
                    return;
                }

                // 6. 查找指定型号的海康相机
                var markCamera = deviceInfoList.Where(item => (item.ModelName == "MV-CS050-10GM" || item.ModelName == "MV-CU013-A0GC")).FirstOrDefault();
                if (markCamera == null)
                {
                    AddLog("未找到型号为 MV-CS050-10GM 或 MV-CU013-A0GC 的海康相机", true);
                    AddLog("检测到的设备列表：");
                    foreach (var device in deviceInfoList)
                    {
                        AddLog($"  - {device.ModelName} (序列号: {device.SerialNumber})");
                    }
                    return;
                }

                AddLog($"找到目标海康相机: {markCamera.ModelName}");

                // 7. 创建并打开设备
                _currentDevice = DeviceFactory.CreateDevice(markCamera);
                if (_currentDevice == null)
                {
                    AddLog("创建设备对象失败", true);
                    CompleteCleanup();
                    return;
                }

                AddLog("设备对象创建成功");

                // **修复：按照海康SDK约束，设备创建后直接调用Open()，不依赖IsConnected判断**
                HKIsOK = _currentDevice.Open();
                if (HKIsOK != MvError.MV_OK)
                {
                    // **修复：针对权限错误提供具体解决建议**
                    if (HKIsOK == MvError.MV_E_ACCESS_DENIED)
                    {
                        AddLog("权限错误解决建议:", true);
                        AddLog("1. 请以管理员权限运行程序", true);
                        AddLog("2. 检查是否有其他程序正在使用该相机", true);
                        AddLog("3. 重启计算机后重试", true);
                        AddLog("4. 检查相机驱动是否正确安装", true);
                    }
                    else if (HKIsOK == MvError.MV_E_RESOURCE)
                    {
                        AddLog("设备资源被占用解决建议:", true);
                        AddLog("1. 关闭所有可能使用相机的程序", true);
                        AddLog("2. 重启相机设备", true);
                        AddLog("3. 检查网络连接状态", true);
                    }
                    else
                    {
                        AddLog($"设备打开失败，错误码: {HKIsOK} - {GetErrorMessage(HKIsOK)}", true);
                    }

                    CompleteCleanup();
                    return;
                }
                AddLog("设备打开成功");

                // **增强：更完善的网络参数配置**
                if (_currentDevice is IGigEDevice gigeDevice)
                {
                    try
                    {
                        // **修复：使用最优网络包大小**
                        int optimalPacketSize;
                        HKIsOK = gigeDevice.GetOptimalPacketSize(out optimalPacketSize);
                        if (HKIsOK == MvError.MV_OK && optimalPacketSize > 0)
                        {
                            HKIsOK = _currentDevice.Parameters.SetIntValue("GevSCPSPacketSize", optimalPacketSize);
                            AddLog($"网络包大小设置为最优值: {optimalPacketSize}");
                        }
                        else
                        {
                            // 使用默认值
                            HKIsOK = _currentDevice.Parameters.SetIntValue("GevSCPSPacketSize", 1500);
                            AddLog("网络包大小设置为默认值: 1500");
                        }

                        // **新增：设置心跳超时，防止网络断线**
                        try
                        {
                            HKIsOK = _currentDevice.Parameters.SetIntValue("GevHeartbeatTimeout", 60000);
                            AddLog("心跳超时设置为60秒");
                        }
                        catch (Exception heartbeatEx)
                        {
                            AddLog($"心跳超时设置失败: {heartbeatEx.Message}", false);
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"网络参数配置失败: {ex.Message}", false);
                    }
                }

                // **修复：恢复足够的设备稳定等待时间**
                Thread.Sleep(800);

                // **简化：只设置最基本的采集参数**
                try
                {
                    HKIsOK = _currentDevice.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                    AddLog("采集模式设置为连续采集");

                    HKIsOK = _currentDevice.Parameters.SetEnumValueByString("TriggerMode", "Off");
                    AddLog("触发模式已关闭");
                }
                catch (Exception ex)
                {
                    AddLog($"基本参数设置失败: {ex.Message}", false);
                }

                // 11. 设置连接状态并触发连接事件
                IsConnected = true;
                AddLog("海康相机连接成功！");

                CameraConnectedEvent?.Invoke();
            }
            catch (Exception ex)
            {
                AddLog($"海康相机连接异常: {ex.Message}", true);
                CompleteCleanup();
            }
            finally
            {
                _isOperating = false;
            }
        }

        /// <summary>
        /// 优化的设备枚举方法 - 使用缓存机制减少枚举时间
        /// </summary>
        private List<IDeviceInfo> GetCachedDeviceList()
        {
            try
            {
                // **检查缓存是否有效**
                if (_cachedDeviceList != null && 
                    (DateTime.Now - _lastEnumTime) < CACHE_VALID_DURATION &&
                    _cachedDeviceList.Count > 0)
                {
                    AddLog($"使用缓存的设备列表 ({_cachedDeviceList.Count} 个设备)");
                    return _cachedDeviceList;
                }

                // **重新枚举设备**
                AddLog("正在枚举海康相机设备（更新缓存）...");
                DeviceTLayerType enumType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice;
                HKIsOK = DeviceEnumerator.EnumDevices(enumType, out deviceInfoList);

                if (HKIsOK != MvError.MV_OK)
                {
                    AddLog($"枚举设备失败，错误码: {HKIsOK}", true);
                    return _cachedDeviceList ?? new List<IDeviceInfo>();
                }

                // **更新缓存**
                _cachedDeviceList = new List<IDeviceInfo>(deviceInfoList);
                _lastEnumTime = DateTime.Now;
                
                AddLog($"设备枚举完成，发现 {_cachedDeviceList.Count} 个设备（已缓存）");
                return _cachedDeviceList;
            }
            catch (Exception ex)
            {
                AddLog($"设备枚举异常: {ex.Message}", true);
                return _cachedDeviceList ?? new List<IDeviceInfo>();
            }
        }

        /// <summary>
        /// 查找目标相机（官方GigE相机连接方式）
        /// **重要修复：基于官方GigE相机连接方式，优先使用设备名称匹配，IP地址作为辅助验证**
        /// </summary>
        private IDeviceInfo FindTargetCamera()
        {
            try
            {
                AddLog("正在使用官方GigE方式查找海康相机设备...");

                if (deviceInfoList == null || deviceInfoList.Count == 0)
                {
                    AddLog("❌ 设备列表为空，无法查找海康相机", true);
                    return null;
                }

                // **获取配置文件中的IP地址**
                string mainCameraIP = _globalState?.AppConfig?.MainCameraAddress ?? "";
                string markCameraIP = _globalState?.AppConfig?.MarkCameraAddress ?? "";

                AddLog($"📋 配置信息:");
                AddLog($"  - 主相机IP (应为OPT): {mainCameraIP}");
                AddLog($"  - Mark相机IP (应为海康): {markCameraIP}");
                AddLog($"  - Mark相机功能: {_globalState?.AppConfig?.HaveMarkCamera}");

                AddLog($"开始筛选 {deviceInfoList.Count} 个设备...");

                // **官方GigE方式：优先使用设备名称匹配**
                AddLog($"📋 工作模式: 官方GigE设备名称匹配 + IP验证");

                // **第一阶段：筛选海康设备**
                var hikvisionDevices = new List<IDeviceInfo>();

                foreach (var device in deviceInfoList)
                {
                    if (device?.ModelName == null)
                    {
                        continue;
                    }

                    string modelName = device.ModelName.ToUpper().Trim();
                    AddLog($"📋 检查设备: {device.ModelName}");

                    // **绝对禁止：任何包含OPT的设备都不允许**
                    if (modelName.Contains("OPT") ||
                        modelName.Contains("OPTMACHINE") ||
                        modelName.Contains("OPTV") ||
                        modelName.StartsWith("OPT-") ||
                        modelName.Contains("YMSCC") ||
                        modelName.Contains("OPTCAM"))
                    {
                        AddLog($"🚫 绝对禁止OPT设备: {device.ModelName}");
                        continue;
                    }

                    // **验证是否为海康设备**
                    bool isHikvisionDevice = modelName.StartsWith("MV-") ||
                                             modelName.StartsWith("MV ") ||
                                             modelName.Contains("HIKVISION") ||
                                             modelName.Contains("HIK");

                    if (isHikvisionDevice)
                    {
                        AddLog($"✅ 发现海康设备: {device.ModelName}");
                        hikvisionDevices.Add(device);
                    }
                    else
                    {
                        AddLog($"❌ 设备 {device.ModelName} 不符合海康设备特征，跳过");
                    }
                }

                if (hikvisionDevices.Count == 0)
                {
                    AddLog($"❌ 未找到任何海康设备", true);
                    return null;
                }

                AddLog($"✅ 找到 {hikvisionDevices.Count} 个海康设备，开始详细验证...");

                // **第二阶段：详细验证和IP匹配**
                foreach (var device in hikvisionDevices)
                {
                    AddLog($"🔍 详细验证设备: {device.ModelName}");

                    try
                    {
                        // **官方方式：尝试创建设备对象**
                        var testDevice = DeviceFactory.CreateDevice(device);
                        if (testDevice == null)
                        {
                            AddLog($"❌ 无法创建设备对象: {device.ModelName}");
                            continue;
                        }

                        AddLog($"✅ 设备对象创建成功: {device.ModelName}");

                        // **获取设备IP地址进行验证**
                        string deviceIP = GetDeviceIPAddress(device);
                        AddLog($"📋 设备IP地址: {deviceIP}");

                        // **IP地址验证逻辑**
                        bool ipValidation = ValidateDeviceIP(deviceIP, mainCameraIP, markCameraIP);

                        if (ipValidation)
                        {
                            AddLog($"✅ IP地址验证通过");
                            AddLog($"🎯 选择海康设备: {device.ModelName} (IP: {deviceIP})");
                            return device;
                        }
                        else
                        {
                            AddLog($"⚠️ IP地址验证未通过，继续检查下一个设备");
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"❌ 验证设备异常: {device.ModelName}, {ex.Message}");
                        continue;
                    }
                }

                // **第三阶段：如果IP验证都失败，选择第一个海康设备**
                if (hikvisionDevices.Count > 0)
                {
                    var firstDevice = hikvisionDevices[0];
                    AddLog($"⚠️ 所有设备IP验证失败，使用第一个海康设备: {firstDevice.ModelName}");
                    AddLog($"🎯 选择海康设备（兼容模式）: {firstDevice.ModelName}");
                    return firstDevice;
                }

                AddLog($"❌ 未找到可用的海康相机设备", true);
                return null;
            }
            catch (Exception ex)
            {
                AddLog($"查找目标相机异常: {ex.Message}", true);
                return null;
            }
        }

        /// <summary>
        /// 验证设备IP地址是否符合要求
        /// **官方GigE方式：灵活的IP验证策略**
        /// </summary>
        /// <param name="deviceIP">设备IP地址</param>
        /// <param name="mainCameraIP">主相机IP</param>
        /// <param name="markCameraIP">Mark相机IP</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateDeviceIP(string deviceIP, string mainCameraIP, string markCameraIP)
        {
            try
            {
                // **策略1：如果无法获取真实IP，允许通过（兼容模式）**
                if (deviceIP.StartsWith("设备_") || deviceIP.Contains("未知") || deviceIP.Contains("获取失败"))
                {
                    AddLog($"    📋 IP验证：兼容模式，允许连接");
                    return true;
                }

                // **策略2：绝对禁止主相机IP**
                if (!string.IsNullOrEmpty(mainCameraIP) &&
                    deviceIP.Equals(mainCameraIP, StringComparison.OrdinalIgnoreCase))
                {
                    AddLog($"    🚫 IP验证：设备IP {deviceIP} 匹配主相机IP，禁止连接");
                    return false;
                }

                // **策略3：优先选择Mark相机IP**
                if (!string.IsNullOrEmpty(markCameraIP) &&
                    deviceIP.Equals(markCameraIP, StringComparison.OrdinalIgnoreCase))
                {
                    AddLog($"    ✅ IP验证：设备IP {deviceIP} 匹配Mark相机IP，优先选择");
                    return true;
                }

                // **策略4：其他IP地址，允许连接**
                AddLog($"    ✅ IP验证：设备IP {deviceIP} 不冲突，允许连接");
                return true;
            }
            catch (Exception ex)
            {
                AddLog($"IP验证异常: {ex.Message}", false);
                return true; // 异常时允许连接
            }
        }

        /// <summary>
        /// 获取设备的IP地址
        /// **官方GigE相机连接方式：通过设备参数直接获取IP地址**
        /// </summary>
        /// <param name="device">设备信息</param>
        /// <returns>设备IP地址</returns>
        private string GetDeviceIPAddress(IDeviceInfo device)
        {
            try
            {
                if (device == null) return "未知设备";

                AddLog($"    🔍 开始获取设备IP地址（官方GigE方式）...");

                // **官方方式1：直接从GigE设备信息获取IP**
                if (device is IGigEDeviceInfo gigeDevice)
                {
                    AddLog($"    📋 GigE设备，使用官方方式获取IP地址...");

                    try
                    {
                        // 尝试从GigE设备信息中直接获取IP地址
                        // 这是海康官方推荐的方式
                        var tempDevice = DeviceFactory.CreateDevice(device);
                        if (tempDevice != null && tempDevice is IGigEDevice gige)
                        {
                            AddLog($"    ✅ GigE设备对象创建成功");

                            try
                            {
                                // 打开设备以获取网络参数
                                if (tempDevice.Open() == MvError.MV_OK)
                                {
                                    AddLog($"    ✅ 设备打开成功，获取网络参数...");

                                    // **官方方式：通过参数获取设备IP地址**
                                    try
                                    {
                                        // 获取设备当前IP地址 - 使用正确的海康SDK方式
                                        IIntValue ipParam = null;
                                        int result = tempDevice.Parameters.GetIntValue("GevCurrentIPAddress", out ipParam);

                                        if (result == MvError.MV_OK && ipParam != null)
                                        {
                                            long deviceIP = ipParam.CurValue;
                                            if (deviceIP != 0)
                                            {
                                                // 将long类型的IP转换为标准IP地址格式
                                                string ipAddress = ConvertLongToIPAddress(deviceIP);
                                                AddLog($"    ✅ 成功获取设备IP地址: {ipAddress}");

                                                tempDevice.Close();
                                                return ipAddress;
                                            }
                                        }

                                        AddLog($"    ⚠️ 无法获取GevCurrentIPAddress参数，错误码: {result}");

                                        // 尝试其他IP相关参数
                                        try
                                        {
                                            IIntValue ipConfigParam = null;
                                            result = tempDevice.Parameters.GetIntValue("GevCurrentIPConfiguration", out ipConfigParam);
                                            if (result == MvError.MV_OK)
                                            {
                                                AddLog($"    📋 IP配置模式: {ipConfigParam?.CurValue}");
                                            }
                                        }
                                        catch (Exception ex2)
                                        {
                                            AddLog($"    ⚠️ 获取IP配置参数异常: {ex2.Message}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        AddLog($"    ⚠️ 获取IP参数异常: {ex.Message}");
                                    }

                                    tempDevice.Close();
                                }
                                else
                                {
                                    AddLog($"    ❌ 设备打开失败");
                                }
                            }
                            catch (Exception ex)
                            {
                                AddLog($"    ❌ 设备操作异常: {ex.Message}");
                            }
                        }
                        else
                        {
                            AddLog($"    ❌ 无法创建GigE设备对象");
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"    ❌ GigE设备处理异常: {ex.Message}");
                    }
                }

                // **备用方式：从设备属性中提取IP地址**
                AddLog($"    📋 尝试从设备属性中提取IP地址...");
                string[] searchProperties = {
                    device.ModelName ?? "",
                    device.SerialNumber ?? "",
                    device.UserDefinedName ?? ""
                };

                foreach (string property in searchProperties)
                {
                    if (!string.IsNullOrEmpty(property))
                    {
                        var ipMatch = System.Text.RegularExpressions.Regex.Match(property, @"(\d+\.\d+\.\d+\.\d+)");
                        if (ipMatch.Success)
                        {
                            string foundIP = ipMatch.Groups[1].Value;
                            AddLog($"    ✅ 从设备属性中找到IP地址: {foundIP}");
                            return foundIP;
                        }
                    }
                }

                // **兼容性模式：使用设备标识**
                string deviceIdentifier = $"设备_{device.ModelName ?? "未知"}";
                AddLog($"    ⚠️ 无法获取IP地址，使用设备标识: {deviceIdentifier}");
                AddLog($"    ⚠️ 将使用设备名称匹配模式进行连接");
                return deviceIdentifier;
            }
            catch (Exception ex)
            {
                AddLog($"获取设备IP地址异常: {ex.Message}", false);
                return $"设备_{device?.ModelName ?? "异常"}";
            }
        }

        /// <summary>
        /// 将long类型的IP地址转换为标准IP地址格式
        /// **官方GigE相机IP地址转换方法**
        /// </summary>
        /// <param name="ipLong">long类型的IP地址</param>
        /// <returns>标准IP地址格式</returns>
        private string ConvertLongToIPAddress(long ipLong)
        {
            try
            {
                // 将long类型IP转换为字节数组
                byte[] bytes = BitConverter.GetBytes((uint)ipLong);

                // 根据字节序调整
                if (BitConverter.IsLittleEndian)
                {
                    Array.Reverse(bytes);
                }

                // 构造IP地址字符串
                return $"{bytes[0]}.{bytes[1]}.{bytes[2]}.{bytes[3]}";
            }
            catch (Exception ex)
            {
                AddLog($"IP地址转换异常: {ex.Message}", false);
                return "IP转换失败";
            }
        }

        /// <summary>
        /// 配置设备参数，增强稳定性
        /// </summary>
        private void ConfigureDeviceParameters()
        {
            try
            {
                        // 配置网络参数（如果是千兆网设备）
                        if (_currentDevice is IGigEDevice)
                        {
                            try
                            {
                                int packetSize;
                                HKIsOK = (_currentDevice as IGigEDevice).GetOptimalPacketSize(out packetSize);
                                if (packetSize > 0)
                                {
                                    HKIsOK = _currentDevice.Parameters.SetIntValue("GevSCPSPacketSize", packetSize);
                                    AddLog($"网络包大小设置为: {packetSize}");
                                }
                        
                        // 设置心跳间隔，防止长时间无操作断线
                        HKIsOK = _currentDevice.Parameters.SetIntValue("GevHeartbeatTimeout", 60000); // 60秒
                        AddLog("网络心跳间隔设置为60秒");
                            }
                            catch (Exception ex)
                            {
                        AddLog($"配置千兆网参数异常: {ex.Message}", false);
                            }
                        }
                        
                        // 设置采集模式
                        try
                        {
                            HKIsOK = _currentDevice.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                            if (HKIsOK == MvError.MV_OK)
                            {
                                AddLog("采集模式设置为连续");
                            }
                            else
                            {
                                AddLog($"设置采集模式失败，错误码: {HKIsOK} - {GetErrorMessage(HKIsOK)}", false);
                            }

                            HKIsOK = _currentDevice.Parameters.SetEnumValueByString("TriggerMode", "Off");
                            if (HKIsOK == MvError.MV_OK)
                            {
                                AddLog("触发模式设置为关闭");
                            }
                            else
                            {
                                AddLog($"设置触发模式失败，错误码: {HKIsOK} - {GetErrorMessage(HKIsOK)}", false);
                            }

                            // **修复：安全设置缓冲区参数，某些相机型号可能不支持此参数**
                            try
                            {
                                // 检查参数是否可用
                                IIntValue frameCountParam = null;
                                int result = _currentDevice.Parameters.GetIntValue("AcquisitionFrameCount", out frameCountParam);

                                if (result == MvError.MV_OK && frameCountParam != null)
                                {
                                    // 参数可用，尝试设置
                                    HKIsOK = _currentDevice.Parameters.SetIntValue("AcquisitionFrameCount", 1);
                                    if (HKIsOK == MvError.MV_OK)
                                    {
                                        AddLog("缓冲区帧数设置为1");
                                    }
                                    else
                                    {
                                        AddLog($"设置缓冲区帧数失败，错误码: {HKIsOK} - {GetErrorMessage(HKIsOK)}", false);
                                    }
                                }
                                else
                                {
                                    AddLog("AcquisitionFrameCount参数不可用，跳过设置（这是正常的，某些相机型号不支持此参数）");
                                }
                            }
                            catch (Exception ex)
                            {
                                AddLog($"设置缓冲区参数异常: {ex.Message}（这通常不影响相机正常工作）", false);
                            }

                        }
                        catch (Exception ex)
                        {
                            AddLog($"设置相机参数异常: {ex.Message}", false);
                        }
                        
                AddLog("设备参数配置完成");
            }
            catch (Exception ex)
            {
                AddLog($"配置设备参数异常: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 完全清理资源，确保干净的状态
        /// </summary>
        private void CompleteCleanup()
        {
            try
            {
                // 停止采集
                _isGrapping = false;
                
                if (_currentDevice != null)
                {
                    // **修复：按照海康SDK约束，严格按顺序清理资源**
                    try
                    {
                        // 1. 先停止采集
                        if (_isGrapping)
                        {
                            _currentDevice.StreamGrabber.StopGrabbing();
                            AddLog("采集已停止", false);
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"停止采集异常: {ex.Message}", false);
                    }

                    try
                    {
                        // 2. 直接关闭设备，不依赖IsConnected判断
                        _currentDevice.Close();
                        AddLog("设备已关闭", false);
                    }
                    catch (Exception ex)
                    {
                        AddLog($"关闭设备异常: {ex.Message}", false);
                    }

                    // 3. 清空设备引用
                    _currentDevice = null;
                }
                
                // 清理帧资源
                try
                {
                    lock (saveImageLock)
                    {
                        if (frameForSave != null)
                        {
                            frameForSave.Dispose();
                            frameForSave = null;
                        }
                    }
                }
                catch { }
                
                // 重置状态
                _isGrapping = false;
                IsConnected = false; // **修复：清理时重置连接状态**

                AddLog("海康相机资源清理完成", false);
                        }
            catch (Exception ex)
            {
                AddLog($"资源清理异常: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 简化的采集线程，忽略 No data 错误，持续采集
        /// </summary>
        private void ReceiveThreadProcess()
        {
            AddLog("采集线程已启动");
            int consecutiveErrors = 0; // 连续错误计数
            const int MAX_CONSECUTIVE_ERRORS = 10; // 最大连续错误次数

            while (_isGrapping && _currentDevice != null)
            {
                try
                {
                    // **修复：检查设备连接状态**
                    if (!_currentDevice.IsConnected)
                    {
                        AddLog("设备连接已断开，采集线程退出", false);
                        break;
                    }

                    IFrameOut frameOut;

                    // 获取相机图像缓冲区
                    HKIsOK = _currentDevice.StreamGrabber.GetImageBuffer(1000, out frameOut);

                    if (HKIsOK == MvError.MV_OK && frameOut != null && frameOut.Image != null)
                    {
                        // **修复：重置错误计数**
                        consecutiveErrors = 0;
                        
                        // 更新帧时间戳
                        _lastFrameTime = DateTime.Now;

                        // **修复：安全的帧处理**
                        try
                        {
                            lock (saveImageLock)
                            {
                                // 释放旧帧
                                if (frameForSave != null)
                                {
                                    frameForSave.Dispose();
                                }
                                frameForSave = frameOut.Clone() as IFrameOut;
                            }
                            RenderImageToWpfImage(frameOut.Image);
                        }
                        catch (Exception renderEx)
                        {
                            // **修复：渲染异常不应中断采集线程**
                            System.Diagnostics.Debug.WriteLine($"[采集线程] 图像渲染异常: {renderEx.Message}");
                        }
                        finally
                        {
                            // **修复：确保释放帧缓冲区**
                            try
                            {
                                HKIsOK = _currentDevice.StreamGrabber.FreeImageBuffer(frameOut);
                            }
                            catch (Exception freeEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[采集线程] 释放帧缓冲区异常: {freeEx.Message}");
                            }
                        }
                    }
                    else if (HKIsOK == MvError.MV_E_NODATA)
                    {
                        // **关键修复：No data 错误不记录，但计入错误计数**
                        consecutiveErrors++;
                        if (consecutiveErrors > MAX_CONSECUTIVE_ERRORS)
                        {
                            AddLog($"⚠️ 连续{MAX_CONSECUTIVE_ERRORS}次无数据，可能存在问题", false);
                            consecutiveErrors = 0; // 重置计数，继续尝试
                        }
                        Thread.Sleep(10); // 短暂等待后继续
                    }
                    else
                    {
                        // **其他错误情况**
                        consecutiveErrors++;
                        if (consecutiveErrors > MAX_CONSECUTIVE_ERRORS)
                        {
                            AddLog($"⚠️ 连续错误过多，错误码: {HKIsOK} - {GetErrorMessage(HKIsOK)}", false);
                            break; // 退出采集线程
                        }
                        Thread.Sleep(50); // 等待后继续
                    }
                }
                catch (Exception threadEx)
                {
                    // **关键修复：采集线程异常处理优化**
                    consecutiveErrors++;
                    System.Diagnostics.Debug.WriteLine($"[采集线程] 异常: {threadEx.Message}");
                    
                    if (consecutiveErrors > MAX_CONSECUTIVE_ERRORS)
                    {
                        AddLog($"⚠️ 采集线程连续异常过多，退出线程", false);
                        break;
                    }
                    Thread.Sleep(100);
                }
                
                // **修复：避免CPU占用过高**
                if (consecutiveErrors == 0)
                {
                    Thread.Sleep(1); // 正常情况下短暂休眠
                }
            }

            // **修复：采集线程退出时清理状态**
            _isGrapping = false;
            AddLog("采集线程已退出");
        }

        /// <summary>
        /// 刷新设备列表并连接
        /// </summary>
        public void RefreshDeviceList()
        {
            AddLog("RefreshDeviceList() 调用，使用新的连接方式");
            RefreshAndConnect();
        }

        /// <summary>
        /// 使用新的连接方式：直接通过设备型号匹配海康相机
        /// 修复连接不上的问题，使用更直接的连接方式
        /// </summary>
        public async void GetFirstOrDefaultCamera()
        {
            try
            {
                AddLog("开始使用新的连接方式连接海康相机...");

                // **强制修改：忽略配置检查，强制连接海康相机**
                AddLog("🔧 强制连接模式：忽略HaveMarkCamera配置，强制连接海康相机");

                // 初始化SDK
                if (!_sdkInited)
                {
                    if (!InitSDK())
                    {
                        AddLog("SDK初始化失败", true);
                        return;
                    }
                }

                // 枚举设备
                DeviceTLayerType enumType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice;
                HKIsOK = DeviceEnumerator.EnumDevices(enumType, out deviceInfoList);

                if (HKIsOK != MvError.MV_OK)
                {
                    AddLog($"枚举设备失败，错误码: {HKIsOK}", true);
                    return;
                }

                AddLog($"发现 {deviceInfoList.Count} 个设备");

                if (deviceInfoList.Count > 0)
                {
                    // 查找指定型号的海康相机
                    var markCamera = deviceInfoList.Where(item => item.ModelName == "MV-CS050-10GM").FirstOrDefault();
                    if (markCamera != null)
                    {
                        AddLog($"找到目标海康相机: {markCamera.ModelName}");

                        // 创建设备
                        if (_currentDevice == null)
                        {
                            _currentDevice = DeviceFactory.CreateDevice(markCamera);
                            AddLog("设备对象创建成功");
                        }

                        // 打开设备
                        if (!_currentDevice.IsConnected)
                        {
                            HKIsOK = _currentDevice.Open();
                            if (HKIsOK != MvError.MV_OK)
                            {
                                AddLog($"打开设备失败，错误码: {HKIsOK}", true);
                                return;
                            }
                            AddLog("设备打开成功");
                        }

                        // 配置千兆网参数
                        if (_currentDevice is IGigEDevice)
                        {
                            int packetSize;
                            HKIsOK = (_currentDevice as IGigEDevice).GetOptimalPacketSize(out packetSize);
                            if (packetSize > 0)
                            {
                                HKIsOK = _currentDevice.Parameters.SetIntValue("GevSCPSPacketSize", packetSize);
                                AddLog($"网络包大小设置为: {packetSize}");
                            }
                        }

                        // 等待设备稳定
                        await Task.Delay(1000);

                        // 设置采集模式为连续采集
                        HKIsOK = _currentDevice.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                        if (HKIsOK != MvError.MV_OK)
                        {
                            AddLog($"设置采集模式失败，错误码: {HKIsOK}", true);
                        }
                        else
                        {
                            AddLog("采集模式设置为连续采集");
                        }

                        // 关闭触发模式
                        HKIsOK = _currentDevice.Parameters.SetEnumValueByString("TriggerMode", "Off");
                        if (HKIsOK != MvError.MV_OK)
                        {
                            AddLog($"关闭触发模式失败，错误码: {HKIsOK}", true);
                        }
                        else
                        {
                            AddLog("触发模式已关闭");
                        }

                        // 设置连接状态并触发连接事件
                        IsConnected = true;
                        AddLog("海康相机连接成功！");

                        if (CameraConnectedEvent != null)
                        {
                            CameraConnectedEvent.Invoke();
                        }
                    }
                    else
                    {
                        AddLog("未找到型号为 MV-CS050-10GM 的海康相机", true);
                    }
                }
                else
                {
                    AddLog("未检测到任何相机设备", true);
                }
            }
            catch (Exception ex)
            {
                AddLog($"连接海康相机异常: {ex.Message}", true);
                _logger.LogError(ex);
            }
        }

        public void StartGrap()
        {
            try
            {
                if (_currentDevice == null || !_currentDevice.IsConnected)
                {
                    AddLog("🔧 设备未连接，尝试重新连接", false);
                    RefreshAndConnect();
                    return;
                }

                // **关键修复：简化状态检查，强制重新启动采集确保画面显示**
                if (_isGrapping)
                {
                    AddLog("⚠️ 采集已在进行中，强制重新启动确保画面正常", false);

                    // **修复：强制停止并重新启动，确保画面能正常显示**
                    try
                    {
                        _currentDevice.StreamGrabber.StopGrabbing();
                        Thread.Sleep(300);
                    }
                    catch (Exception stopEx)
                    {
                        AddLog($"停止采集异常: {stopEx.Message}", false);
                    }

                    _isGrapping = false;
                    Thread.Sleep(200);
                    AddLog("🔄 强制重新启动采集以确保画面显示");
                }

                AddLog("▶️ 启动海康相机采集（优化模式）...");

                // **确保StreamGrabber存在**
                if (_currentDevice.StreamGrabber == null)
                {
                    AddLog("❌ StreamGrabber为空，无法启动采集", true);
                    return;
                }

                // **修复：在启动前确保之前的采集完全停止**
                try
                {
                    // 确保停止任何可能存在的采集
                    _currentDevice.StreamGrabber.StopGrabbing();
                    Thread.Sleep(200);
                }
                catch
                {
                    // 忽略停止时的异常，继续启动
                }

                // **性能优化：直接启动采集，不设置任何额外参数**
                HKIsOK = _currentDevice.StreamGrabber.StartGrabbing();
                if (HKIsOK != MvError.MV_OK)
                {
                    AddLog($"❌ 启动采集失败，错误码: {HKIsOK}", true);
                    _needReconnect = true;
                    return;
                }

                _isGrapping = true;
                _lastFrameTime = DateTime.Now;

                // **修复：确保采集线程启动前状态正确**
                AddLog("🔄 启动采集线程...");

                // **关键修复：确保采集线程正确启动并立即开始渲染画面**
                AddLog("🔄 启动采集线程并开始画面渲染...");

                Task.Factory.StartNew(ReceiveThreadProcess, TaskCreationOptions.LongRunning)
                    .ContinueWith(t =>
                    {
                        if (t.IsFaulted)
                        {
                            AddLog($"❌ 采集线程异常: {t.Exception?.GetBaseException()?.Message}", true);
                            _isGrapping = false; // 确保状态正确
                        }
                        else
                        {
                            AddLog("📝 采集线程正常退出");
                        }
                    });

                // **修复：等待采集线程启动并验证画面渲染**
                Thread.Sleep(500);

                // **关键修复：验证采集是否真正开始工作**
                bool verifySuccess = false;
                for (int i = 0; i < 10; i++) // 最多等待1秒
                {
                    if (_isGrapping)
                    {
                        // 尝试获取一帧验证采集是否正常
                        try
                        {
                            IFrameOut testFrame;
                            int testResult = _currentDevice.StreamGrabber.GetImageBuffer(100, out testFrame);
                            if (testResult == MvError.MV_OK && testFrame != null)
                            {
                                _currentDevice.StreamGrabber.FreeImageBuffer(testFrame);
                                verifySuccess = true;
                                AddLog("✅ 采集验证成功，画面应该开始显示");
                                break;
                            }
                        }
                        catch { }
                    }
                    Thread.Sleep(100);
                }

                if (verifySuccess)
                {
                    AddLog("✅ 海康相机采集启动成功，画面渲染正常");
                }
                else
                {
                    AddLog("⚠️ 采集启动但验证失败，可能需要重试", false);
                }
            }
            catch (Exception ex)
            {
                AddLog($"启动采集异常: {ex.Message}", true);
                _isGrapping = false; // 确保异常时状态正确
                _needReconnect = true;
            }
        }

        /// <summary>
        /// 验证IImage对象的有效性
        /// </summary>
        /// <param name="iImage">要验证的图像对象</param>
        /// <returns>如果图像有效返回true，否则返回false</returns>
        private bool ValidateIImage(IImage iImage)
        {
            if (iImage == null)
            {
                _logger.LogInfo("⚠️ IImage对象为null");
                return false;
            }

            if (iImage.Width <= 0 || iImage.Height <= 0)
            {
                _logger.LogInfo($"⚠️ IImage尺寸无效: {iImage.Width}x{iImage.Height}");
                return false;
            }

            // 检查图像尺寸是否合理（避免过大的图像导致内存问题）
            const int MAX_WIDTH = 8192;
            const int MAX_HEIGHT = 8192;
            if (iImage.Width > MAX_WIDTH || iImage.Height > MAX_HEIGHT)
            {
                _logger.LogInfo($"⚠️ IImage尺寸过大: {iImage.Width}x{iImage.Height}，最大支持: {MAX_WIDTH}x{MAX_HEIGHT}");
                return false;
            }

            return true;
        }

        private void RenderImageToWpfImage(IImage iImage)
        {
            // **关键修复：使用专门的验证方法**
            if (!ValidateIImage(iImage))
                return;

            try
            {
                // 安全检查：确保Application.Current不为null，避免NullReferenceException
                if (Application.Current?.Dispatcher == null)
                {
                    _logger.LogInfo("Application.Current或Dispatcher为null，无法更新UI画面");
                    return;
                }

                // **关键修复：在UI线程外进行bitmap转换，减少UI线程阻塞和内存访问冲突**
                Bitmap bitmap = null;
                BitmapSource bitmapSource = null;

                try
                {
                    // 在后台线程进行bitmap转换
                    bitmap = iImage.ToBitmap();
                    if (bitmap == null)
                    {
                        _logger.LogInfo("⚠️ ToBitmap()返回null，跳过此帧");
                        return;
                    }

                    // **关键修复：增加bitmap有效性验证**
                    if (bitmap.Width <= 0 || bitmap.Height <= 0)
                    {
                        _logger.LogInfo($"⚠️ Bitmap尺寸无效: {bitmap.Width}x{bitmap.Height}");
                        return;
                    }

                    IntPtr hBitmap = IntPtr.Zero;
                    try
                    {
                        // **关键修复：增加GetHbitmap的安全检查**
                        hBitmap = bitmap.GetHbitmap();
                        if (hBitmap == IntPtr.Zero)
                        {
                            _logger.LogError("GetHbitmap()返回无效句柄");
                            return;
                        }

                        // **关键修复：创建BitmapSource并立即冻结，确保线程安全**
                        bitmapSource = System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(
                            hBitmap,
                            IntPtr.Zero,
                            Int32Rect.Empty,
                            BitmapSizeOptions.FromEmptyOptions());

                        // **关键修复：冻结BitmapSource以确保跨线程访问安全**
                        if (bitmapSource.CanFreeze)
                        {
                            bitmapSource.Freeze();
                        }
                    }
                    finally
                    {
                        // **关键修复：确保在任何情况下都释放GDI句柄**
                        if (hBitmap != IntPtr.Zero)
                        {
                            DeleteObject(hBitmap);
                        }
                    }

                    // **关键修复：确保事件在UI线程中正确触发**
                    if (bitmapSource != null)
                    {

                        if (Application.Current?.Dispatcher != null)
                        {
                            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                            {
                                try
                                {
                                    if (UpdateGrappingEvent != null)
                                    {
                                        UpdateGrappingEvent.Invoke(bitmapSource);
                                      
                                    }
                                    else
                                    {
                                        AddLog("⚠️ UpdateGrappingEvent为null，无法触发画面更新", true);
                                    }
                                }
                                catch (Exception uiEx)
                                {
                                    _logger.LogError($"UI更新异常: {uiEx.Message}", uiEx);
                                }
                            }));
                        }
                        else
                        {
                            AddLog("⚠️ Application.Current.Dispatcher为null", true);
                        }
                    }
                    else
                    {
                        AddLog("⚠️ bitmapSource为null，无法更新画面", false);
                    }
                }
                finally
                {
                    // **关键修复：确保bitmap资源得到正确释放**
                    bitmap?.Dispose();
                }
            }
            catch (AccessViolationException avEx)
            {
                _logger.LogError($"内存访问违规异常: {avEx.Message}", avEx);
                _logger.LogError("可能原因：图像数据损坏或内存已被释放");
                _logger.LogInfo("尝试使用安全模式重新渲染图像...");

                // **关键修复：当主要方法失败时，使用安全的备用方法**
                try
                {
                    RenderImageToWpfImageSafe(iImage);
                }
                catch (Exception safeEx)
                {
                    _logger.LogError($"安全模式也失败: {safeEx.Message}", safeEx);
                }
            }
            catch (OutOfMemoryException oomEx)
            {
                _logger.LogError($"内存不足异常: {oomEx.Message}", oomEx);
                // 触发垃圾回收尝试释放内存
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // 内存不足时也尝试使用安全模式
                try
                {
                    _logger.LogInfo("内存不足，尝试使用安全模式...");
                    RenderImageToWpfImageSafe(iImage);
                }
                catch (Exception safeEx)
                {
                    _logger.LogError($"内存不足时安全模式也失败: {safeEx.Message}", safeEx);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"RenderImageToWpfImage异常: {ex.Message}", ex);

                // 对于其他异常也尝试安全模式
                try
                {
                    _logger.LogInfo("尝试使用安全模式作为备用方案...");
                    RenderImageToWpfImageSafe(iImage);
                }
                catch (Exception safeEx)
                {
                    _logger.LogError($"备用安全模式失败: {safeEx.Message}", safeEx);
                }
            }
        }

        /// <summary>
        /// 安全的图像渲染方法 - 使用内存流方式避免AccessViolationException
        /// 当主要方法失败时可以作为备用方案
        /// </summary>
        /// <param name="iImage">海康相机图像对象</param>
        private void RenderImageToWpfImageSafe(IImage iImage)
        {
            if (iImage == null || iImage.Width <= 0 || iImage.Height <= 0)
                return;

            try
            {
                if (Application.Current?.Dispatcher == null)
                {
                    _logger.LogInfo("Application.Current或Dispatcher为null，无法更新UI画面");
                    return;
                }

                // **安全方法：使用内存流转换，避免直接的GDI句柄操作**
                using (var bitmap = iImage.ToBitmap())
                {
                    if (bitmap == null) return;

                    using (var memory = new System.IO.MemoryStream())
                    {
                        // 将bitmap保存到内存流
                        bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Bmp);
                        memory.Position = 0;

                        // 从内存流创建BitmapImage
                        var bitmapImage = new BitmapImage();
                        bitmapImage.BeginInit();
                        bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                        bitmapImage.StreamSource = memory;
                        bitmapImage.EndInit();
                        bitmapImage.Freeze(); // 冻结以确保线程安全

                        // 在UI线程中更新
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                UpdateGrappingEvent?.Invoke(bitmapImage);
                            }
                            catch (Exception uiEx)
                            {
                                _logger.LogError($"安全模式UI更新异常: {uiEx.Message}", uiEx);
                            }
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"RenderImageToWpfImageSafe异常: {ex.Message}", ex);
            }
        }

        private void RenderVideo(BitmapSource bitmapSource)
        {

            UpdateGrappingEvent?.Invoke(bitmapSource);
        }

        // PInvoke 用于释放 Bitmap 句柄
        [System.Runtime.InteropServices.DllImport("gdi32.dll")]
        public static extern bool DeleteObject(IntPtr hObject);

        public void TakePicture(string folderName, string pictureName)
        {

            if (_currentDevice == null || !_isGrapping) return;
            ImageFormatInfo imageFormatInfo;
            imageFormatInfo.FormatType = ImageFormatType.Jpeg;
            imageFormatInfo.JpegQuality = 80;
            SaveImage(folderName, pictureName, imageFormatInfo);


        }

        private void SaveImage(string folderName, string imageName, ImageFormatInfo imageFormatInfo)
        {
            string folder = _globalState.AppConfig.SaveFolder;
            if (frameForSave == null) return;
            lock (saveImageLock)
            {
                string filename = imageName + "." + imageFormatInfo.FormatType.ToString().ToLower();
                filename = Camera.Com.FileHelper.GenerateFileName(folderName, filename);
                string resultPath = Camera.Com.FileHelper.ConcatFile(folderName, filename);

                HKIsOK = _currentDevice.ImageSaver.SaveImageToFile(resultPath, frameForSave.Image, imageFormatInfo, CFAMethod.Equilibrated);
                if (HKIsOK == MvError.MV_OK) UpdatePictureModelEvent.Invoke(new PictureModel() { FileName = filename, FileFullPath = resultPath });
            }
        }

        public void StopGrap()
        {
            try
            {
                AddLog("🛑 正在停止海康相机采集...");

                // **修复：先设置停止标志，确保采集线程能够收到停止信号**
                _isGrapping = false;

                if (_currentDevice == null)
                {
                    AddLog("设备为空，采集已标记为停止");
                    return;
                }

                // **修复：给采集线程一些时间来响应停止信号**
                Thread.Sleep(300);

                // **修复：确保StreamGrabber存在并尝试停止**
                if (_currentDevice.StreamGrabber != null)
                {
                    try
                    {
                        HKIsOK = _currentDevice.StreamGrabber.StopGrabbing();
                        if (HKIsOK == MvError.MV_OK)
                        {
                            AddLog("✅ 海康相机采集已成功停止");
                        }
                        else
                        {
                            AddLog($"⚠️ 停止采集返回错误码: {HKIsOK} - {GetErrorMessage(HKIsOK)}", false);
                            // **修复：即使返回错误码，也认为停止成功，因为_isGrapping已设为false**
                        }
                    }
                    catch (Exception stopEx)
                    {
                        AddLog($"⚠️ 停止采集异常: {stopEx.Message}", false);
                        // **修复：异常时也认为停止成功，因为_isGrapping已设为false**
                    }
                }
                else
                {
                    AddLog("⚠️ StreamGrabber为空，但采集状态已标记为停止");
                }

                // **修复：额外等待确保采集线程完全退出**
                Thread.Sleep(200);

                // **修复：验证停止状态**
                if (!_isGrapping)
                {
                    AddLog("✅ 海康相机采集已确认停止");
                }
                else
                {
                    AddLog("⚠️ 采集状态异常，强制设置为停止", false);
                    _isGrapping = false;
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 停止采集异常: {ex.Message}", false);
                _logger.LogError($"停止海康相机采集异常: {ex.Message}");

                // **确保状态正确设置**
                _isGrapping = false;
                AddLog("✅ 采集状态已强制设置为停止");
            }
        }

        public void CloseCamera()
        {
            try
            {
                SafeCloseCurrentDevice();
            }
            catch (Exception ex)
            {
                AddLog($"关闭相机异常: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取错误信息描述
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <returns>错误信息</returns>
        private string GetErrorMessage(int errorCode)
        {
            switch (errorCode)
            {
                case MvError.MV_E_HANDLE: return "Error or invalid handle";
                case MvError.MV_E_SUPPORT: return "Not supported function";
                case MvError.MV_E_BUFOVER: return "Cache is full";
                case MvError.MV_E_CALLORDER: return "Function calling order error";
                case MvError.MV_E_PARAMETER: return "Incorrect parameter";
                case MvError.MV_E_RESOURCE: return "Applying resource failed";
                case MvError.MV_E_NODATA: return "No data";
                case MvError.MV_E_PRECONDITION: return "Precondition error, or running environment changed";
                case MvError.MV_E_VERSION: return "Version mismatches";
                case MvError.MV_E_NOENOUGH_BUF: return "Insufficient memory";
                case MvError.MV_E_UNKNOW: return "Unknown error";
                case MvError.MV_E_GC_GENERIC: return "General error";
                case MvError.MV_E_GC_ACCESS: return "Node accessing condition error";
                case MvError.MV_E_ACCESS_DENIED: return "No permission";
                case MvError.MV_E_BUSY: return "Device is busy, or network disconnected";
                case MvError.MV_E_NETER: return "Network error";
                case 9001: return "连接HK相机失败，发生未知异常！";
                case 9002: return "照片存储异常，未获取到实时视频流!";
                default: return $"未知错误码: {errorCode}";
            }
        }

        private void ErrorHappend(int errorCode, string message)
        {
            string errorMsg = message + " " + GetErrorMessage(errorCode);
            _logger.LogInfo(errorMsg);
            HKErrorEvent?.Invoke(errorMsg);
        }

        private void AddLog(string message, bool isError = false)
        {
            try
            {
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    EventCode = isError ? LEvents.HEventCode.ERROR : LEvents.HEventCode.SUCCESS,
                    StatusShowType = LEvents.ShowType.ALL,
                    SourceType = LEvents.SourceType.LIGHT,
                    EventMessage = $"[HK相机] {message}",
                    EquipmentStatus = isError ? 4 : 1
                });
            }
            catch { }
        }

        private void SafeCloseCurrentDevice()
        {
            try
            {
                if (_currentDevice != null)
                {
                    AddLog("正在关闭海康相机连接...");
                    
                    // 先停止采集
                    if (_isGrapping)
                    {
                        try
                        {
                            _isGrapping = false;
                            _currentDevice.StreamGrabber.StopGrabbing();
                            AddLog("相机采集已停止");
                        }
                        catch (Exception ex)
                        {
                            AddLog($"停止采集异常: {ex.Message}", false);
                        }
                    }
                    
                    // 关闭设备
                    try
                    {
                        if (_currentDevice.IsConnected)
                        {
                            _currentDevice.Close();
                            AddLog("设备连接已关闭");
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"关闭设备异常: {ex.Message}", false);
                    }
                    
                    _currentDevice = null;
                    AddLog("海康相机已安全关闭");
                }
            }
            catch (Exception ex)
            {
                AddLog($"关闭海康相机时发生异常: {ex.Message}", false);
            }
        }

        public bool InitSDK()
        {
            HKIsOK = SDKSystem.Initialize();
            _sdkInited = HKIsOK == MvError.MV_OK;
            return _sdkInited;
        }

        public void SetPictureName(string folderName, string pictueName)
        {
            _pictureFolder = folderName;
            _pictureName = pictueName;
        }

        public void Dispose()
        {
            try
            {
                AddLog("开始释放海康相机资源...");
                
                // **安全修复：标记为正在释放状态，防止其他操作干扰**
                _isOperating = true;
                
                // 1. 停止监控定时器
                try
                {
                    _connectionCheckTimer?.Dispose();
                    _connectionCheckTimer = null;
                    AddLog("连接监控定时器已停止");
                }
                catch (Exception ex)
                {
                    AddLog($"停止连接监控定时器异常: {ex.Message}", false);
                }
                
                // 2. 使用统一清理方法
                try
                {
                    CompleteCleanup();
                }
                catch (Exception ex)
                {
                    AddLog($"统一清理异常: {ex.Message}", false);
                }
                
                // 3. 等待所有操作完成
                Thread.Sleep(500);
                
                // 4. 清理设备列表
                try
                {
                    if (deviceInfoList != null)
                    {
                        deviceInfoList.Clear();
                        AddLog("设备信息列表已清理");
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"清理设备列表异常: {ex.Message}", false);
                }
                
                // 5. 清理状态标志
                try
                {
                    _isGrapping = false;
                    _isOperating = false;
                    _needReconnect = false;
                    AddLog("状态标志已重置");
                }
                catch (Exception ex)
                {
                    AddLog($"重置状态标志异常: {ex.Message}", false);
                }
                
                // 6. **修复：安全清理事件订阅，避免在清理过程中触发事件**
                try
                {
                    AddLog("开始安全清理事件订阅...");

                    // **关键修复：不清空事件订阅，避免在程序退出时出现空引用异常**
                    // 让事件订阅保持，由垃圾回收器自然清理
                    // UpdateGrappingEvent = null;
                    // HKErrorEvent = null;
                    // CameraConnectedEvent = null;
                    // DisConnectEvent = null;
                    // UpdatePictureModelEvent = null;
                    // BeforeTakePictureEvent = null;

                    AddLog("✅ 事件订阅保持不变，避免清理时的潜在问题");
                }
                catch (Exception ex)
                {
                    AddLog($"事件订阅处理异常: {ex.Message}", false);
                }
                
                // **关键修复：分步骤安全释放SDK，添加多重保护**
                try
                {
                    AddLog("开始安全释放SDK...");
                    
                    // 7a. 先检查SDK状态
                    if (_sdkInited)
                    {
                        AddLog("SDK已初始化，准备释放...");
                        
                        // 7b. 额外等待，确保所有设备操作完全结束
                        Thread.Sleep(1000);
                        
                        // 7c. 强制垃圾回收，清理可能的未释放资源
                        try
                        {
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                            GC.Collect();
                            AddLog("垃圾回收完成");
                        }
                        catch (Exception gcEx)
                        {
                            AddLog($"垃圾回收异常: {gcEx.Message}", false);
                        }
                        
                        // 7d. 尝试释放SDK（最危险的操作）
                        try
                        {
                            AddLog("⚠️ 跳过SDKSystem.Finalize()调用，避免程序崩溃");

                            // **关键修复：不调用SDKSystem.Finalize()，因为它会导致程序崩溃**
                            // int finalizeResult = SDKSystem.Finalize();
                            int finalizeResult = MvError.MV_OK; // 模拟成功
                            
                            if (finalizeResult == MvError.MV_OK)
                            {
                                AddLog("✅ 海康相机SDK状态已安全重置（跳过Finalize调用）");
                            }
                            else
                            {
                                AddLog($"⚠️ SDK状态重置警告，错误码: {finalizeResult}", false);
                            }

                            _sdkInited = false;
                            AddLog("✅ SDK状态已重置，程序不会因SDK释放而崩溃");
                            AddLog("ℹ️ 系统将在程序退出时自动释放所有SDK资源");
                        }
                        catch (AccessViolationException avEx)
                        {
                            // **关键修复：专门处理访问违规异常，这是最常见的SDK释放异常**
                            AddLog($"⚠️ SDK释放时访问违规异常: {avEx.Message}", false);
                            AddLog("SDK可能已被系统强制释放，标记为未初始化状态");
                            _sdkInited = false;
                        }
                        catch (System.Runtime.InteropServices.SEHException sehEx)
                        {
                            // **关键修复：处理结构化异常处理异常**
                            AddLog($"⚠️ SDK释放时SEH异常: {sehEx.Message}", false);
                            AddLog("SDK释放过程中发生系统级异常，但程序将继续运行");
                            _sdkInited = false;
                        }
                        catch (Exception sdkEx)
                        {
                            // **关键修复：处理其他SDK释放异常**
                            AddLog($"⚠️ SDK释放异常: {sdkEx.Message}", false);
                            AddLog("SDK释放失败，但程序将继续运行");
                            _sdkInited = false;
                        }
                    }
                    else
                    {
                        AddLog("SDK未初始化，跳过释放");
                    }
                }
                catch (Exception sdkContainerEx)
                {
                    // **最后防线：捕获SDK释放容器异常**
                    AddLog($"❌ SDK释放容器异常: {sdkContainerEx.Message}", false);
                    _sdkInited = false;
                }
                
                AddLog("✅ 海康相机资源释放完成");
            }
            catch (Exception ex)
            {
                // **最终安全网：防止Dispose方法本身崩溃程序**
                AddLog($"❌ 释放海康相机资源时发生严重异常: {ex.Message}", true);
                
                // **确保关键状态被重置，即使发生异常**
                try
                {
                    _sdkInited = false;
                    _isGrapping = false;
                    _isOperating = false;
                }
                catch { }
            }
        }

        /// <summary>
        /// 尝试通过设备对象直接获取真实IP地址
        /// **专门针对海康相机的IP获取方法**
        /// </summary>
        /// <param name="device">设备信息</param>
        /// <returns>真实IP地址</returns>
        private string TryGetRealDeviceIP(IDeviceInfo device)
        {
            try
            {
                if (device == null) return "设备_空";

                // 对于海康相机，尝试通过网络发现获取IP
                // 这是一个简化的实现，实际可能需要更复杂的网络扫描

                // 策略1：检查设备是否在常见的IP段
                string[] commonIPRanges = {
                    "192.168.0.", "192.168.1.", "192.168.2.",
                    "10.0.0.", "172.16.0."
                };

                // 策略2：如果设备名称包含特定信息，尝试解析
                string modelName = device.ModelName ?? "";
                string serialNumber = device.SerialNumber ?? "";
                string userDefinedName = device.UserDefinedName ?? "";

                // 检查所有属性中是否包含IP信息
                string[] allProperties = { modelName, serialNumber, userDefinedName };
                foreach (string prop in allProperties)
                {
                    if (!string.IsNullOrEmpty(prop))
                    {
                        // 查找IP地址模式
                        var ipMatch = System.Text.RegularExpressions.Regex.Match(prop, @"(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})");
                        if (ipMatch.Success)
                        {
                            string foundIP = ipMatch.Groups[1].Value;
                            // 验证IP地址的有效性
                            if (IsValidIPAddress(foundIP))
                            {
                                return foundIP;
                            }
                        }
                    }
                }

                // 策略3：如果是GigE设备，尝试通过MAC地址或其他方式推断
                if (device is IGigEDeviceInfo)
                {
                    // 这里可以添加更复杂的网络发现逻辑
                    // 但为了简化，暂时返回标识
                    return $"设备_GigE_{device.ModelName}";
                }

                return $"设备_{device.ModelName ?? "未知"}";
            }
            catch (Exception ex)
            {
                AddLog($"获取真实设备IP异常: {ex.Message}", false);
                return $"设备_异常_{device?.ModelName ?? "未知"}";
            }
        }

        /// <summary>
        /// 验证IP地址的有效性
        /// </summary>
        /// <param name="ip">IP地址字符串</param>
        /// <returns>是否有效</returns>
        private bool IsValidIPAddress(string ip)
        {
            try
            {
                if (string.IsNullOrEmpty(ip)) return false;

                string[] parts = ip.Split('.');
                if (parts.Length != 4) return false;

                foreach (string part in parts)
                {
                    if (!int.TryParse(part, out int num) || num < 0 || num > 255)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 诊断海康相机连接问题（供GlobalCameraManager调用）
        /// **新增功能：全面诊断海康相机连接问题和设备识别**
        /// </summary>
        public void DiagnoseHKCameraIssues()
        {
            try
            {
                AddLog("=== 🔧 开始海康相机连接问题诊断 ===");

                // 1. 检查当前连接状态
                AddLog($"📋 步骤1: 检查当前连接状态");
                AddLog($"  - IsConnected: {IsConnected}");
                AddLog($"  - SDKInited: {SDKInited}");
                AddLog($"  - DeviceCount: {deviceInfoList?.Count ?? 0}");
                AddLog($"  - CurrentDevice: {(_currentDevice != null ? "已创建" : "未创建")}");

                // 2. 检查配置信息
                AddLog($"📋 步骤2: 检查配置信息");
                string mainCameraIP = _globalState?.AppConfig?.MainCameraAddress ?? "";
                string markCameraIP = _globalState?.AppConfig?.MarkCameraAddress ?? "";
                var markCameraEnabled = _globalState?.AppConfig?.HaveMarkCamera ?? OpenEnum.CLOSE;

                AddLog($"  - Mark相机功能: {markCameraEnabled}");
                AddLog($"  - 主相机IP: {mainCameraIP}");
                AddLog($"  - Mark相机IP: {markCameraIP}");

                // **强制修改：忽略配置状态，继续诊断**
                AddLog($"  🔧 强制连接模式：忽略Mark相机配置状态，继续诊断连接问题");

                // 3. 重新枚举设备检查
                AddLog($"📋 步骤3: 重新枚举设备检查");
                try
                {
                    // 重新枚举设备
                    DeviceTLayerType enumType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice;
                    HKIsOK = DeviceEnumerator.EnumDevices(enumType, out deviceInfoList);

                    if (HKIsOK == MvError.MV_OK)
                    {
                        AddLog($"  ✅ 设备枚举成功，发现 {deviceInfoList?.Count ?? 0} 个设备");
                    }
                    else
                    {
                        AddLog($"  ❌ 设备枚举失败，错误码: {HKIsOK}");
                        deviceInfoList = new List<IDeviceInfo>();
                    }

                    if (deviceInfoList != null && deviceInfoList.Count > 0)
                    {
                        AddLog($"  📋 开始分析设备类型...");

                        foreach (var device in deviceInfoList)
                        {
                            if (device?.ModelName == null) continue;

                            string modelName = device.ModelName.ToUpper().Trim();
                            AddLog($"    设备: {device.ModelName}");

                            // 检查是否是海康设备
                            bool isHikvisionDevice = modelName.StartsWith("MV-") ||
                                                     modelName.StartsWith("MV ") ||
                                                     modelName.Contains("HIKVISION") ||
                                                     modelName.Contains("HIK");

                            if (isHikvisionDevice)
                            {
                                AddLog($"      ✅ 海康设备特征匹配");

                                // 检查IP地址
                                string deviceIP = GetDeviceIPAddress(device);
                                AddLog($"      📋 设备IP: {deviceIP}");

                                // 检查IP匹配情况
                                if (!string.IsNullOrEmpty(markCameraIP))
                                {
                                    if (deviceIP.Equals(markCameraIP, StringComparison.OrdinalIgnoreCase))
                                    {
                                        AddLog($"      ✅ IP地址匹配Mark相机配置");
                                    }
                                    else if (deviceIP.StartsWith("设备_") || deviceIP.Contains("未知"))
                                    {
                                        AddLog($"      ⚠️ 无法获取IP地址，但允许连接");
                                    }
                                    else
                                    {
                                        AddLog($"      ❌ IP地址不匹配Mark相机配置");
                                    }
                                }
                                else
                                {
                                    AddLog($"      ⚠️ 未配置Mark相机IP，使用设备名称匹配");
                                }

                                // 尝试创建设备对象
                                try
                                {
                                    var testDevice = DeviceFactory.CreateDevice(device);
                                    if (testDevice != null)
                                    {
                                        AddLog($"      ✅ 设备对象创建成功");
                                    }
                                    else
                                    {
                                        AddLog($"      ❌ 设备对象创建失败");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    AddLog($"      ❌ 设备对象创建异常: {ex.Message}");
                                }
                            }
                            else
                            {
                                AddLog($"      ❌ 不是海康设备");
                            }
                        }

                        // 尝试查找目标相机
                        var targetDevice = FindTargetCamera();
                        if (targetDevice != null)
                        {
                            AddLog($"  ✅ 找到目标海康设备: {targetDevice.ModelName}");
                        }
                        else
                        {
                            AddLog($"  ❌ 未找到符合条件的海康设备");
                        }
                    }
                    else
                    {
                        AddLog($"  ❌ 未发现任何设备");
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"  ❌ 设备枚举异常: {ex.Message}");
                }

                // 4. 提供诊断建议
                AddLog($"📋 步骤4: 诊断建议");
                if (!IsConnected)
                {
                    AddLog($"  🔧 海康相机未连接的可能解决方案:");
                    AddLog($"    1. 检查Mark相机功能是否已开启");
                    AddLog($"    2. 检查海康相机电源和网络连接");
                    AddLog($"    3. 确认海康相机IP地址配置正确");
                    AddLog($"    4. 检查海康相机型号是否以MV-开头");
                    AddLog($"    5. 重启海康相机设备");
                    AddLog($"    6. 以管理员权限重新运行程序");
                    AddLog($"    7. 检查海康SDK是否正确安装");
                }
                else
                {
                    AddLog($"  ✅ 海康相机连接正常");
                }

                AddLog("=== 🔧 海康相机连接问题诊断完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"海康相机诊断过程异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 强制启用Mark相机功能并尝试连接（供HKVisionControlViewModel调用）
        /// </summary>
        public async Task<bool> ForceEnableAndConnectAsync()
        {
            try
            {
                AddLog("强制启用Mark相机功能并尝试连接...");

                // 1. 启用Mark相机功能
                if (_globalState?.AppConfig != null)
                {
                    _globalState.AppConfig.HaveMarkCamera = OpenEnum.OPEN;
                    AddLog("Mark相机功能已启用");
                }

                // 2. 尝试连接
                await Task.Run(() => RefreshAndConnect());

                // 3. 等待连接状态稳定
                await Task.Delay(2000);

                // 4. 检查连接结果
                bool connected = IsConnected;
                AddLog($"强制连接结果: {(connected ? "成功" : "失败")}");

                return connected;
            }
            catch (Exception ex)
            {
                AddLog($"强制启用并连接Mark相机异常: {ex.Message}", true);
                return false;
            }
        }

    }
}

