using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.ViewModel.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using System.Diagnostics;
using System.IO;
using System.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.ComponentModel;

namespace IPM.Vision.ViewModel
{

    public class TestWindowViewModel : ViewModelBase, INotifyPropertyChanged, IDisposable
    {
        private readonly OptVisionService _optVisionService;
        private readonly NLogHelper _logger;
        private readonly ObservableGlobalState _globalState;
        private string _timeSpan = "1000"; // 默认1秒间隔
        private bool _isAutoTriggerRunning = false;
        private CancellationTokenSource _cancellationTokenSource;
        private Timer _autoTriggerTimer;

        public TestWindowViewModel(OptVisionService optVisionService, NLogHelper logger, ObservableGlobalState globalState)
        {
            _optVisionService = optVisionService;
            _logger = logger;
            _globalState = globalState;
        }

        public string TimeSpan
        {
            get => _timeSpan;
            set => SetProperty(ref _timeSpan, value);
        }

        public bool IsAutoTriggerRunning
        {
            get => _isAutoTriggerRunning;
            set => SetProperty(ref _isAutoTriggerRunning, value);
        }

        /// <summary>
        /// 设置软触发模式
        /// </summary>
        public IRelayCommand SetCameraModeCommand => new RelayCommand(() =>
        {
            try
            {
                _logger.LogInfo("开始设置OPT相机软触发模式");
                
                // 检查相机连接状态
                if (!_optVisionService.IsConnect)
                {
                    _logger.LogError("OPT相机未连接，无法设置软触发模式");
                    return;
                }

                // 设置软触发配置
                _optVisionService.SetSoftwareTriggerConf();
                _logger.LogInfo("OPT相机软触发模式设置完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"设置OPT相机软触发模式失败: {ex.Message}");
            }
        });

        /// <summary>
        /// 取消软触发模式，恢复实时画面
        /// </summary>
        public IRelayCommand CancelTriggerModeCommand => new RelayCommand(() =>
        {
            try
            {
                _logger.LogInfo("开始取消OPT相机软触发模式，恢复实时画面");
                
                // 检查相机连接状态
                if (!_optVisionService.IsConnect)
                {
                    _logger.LogError("OPT相机未连接，无法取消软触发模式");
                    return;
                }

                // 首先停止自动触发（如果正在运行）
                if (_isAutoTriggerRunning)
                {
                    StopAutoTrigger();
                    _logger.LogInfo("已停止自动触发");
                }

                // 关闭触发模式，恢复连续采集
                _optVisionService.CloseTrigger();
                _logger.LogInfo("OPT相机软触发模式已取消，恢复实时画面模式");
            }
            catch (Exception ex)
            {
                _logger.LogError($"取消OPT相机软触发模式失败: {ex.Message}");
            }
        });

        /// <summary>
        /// 单次触发拍照
        /// </summary>
        public IRelayCommand TriggerOneTimes => new RelayCommand(() =>
        {
            try
            {
                _logger.LogInfo("开始执行单次软触发拍照");
                
                // 检查相机连接状态
                if (!_optVisionService.IsConnect)
                {
                    _logger.LogError("OPT相机未连接，无法执行软触发");
                    return;
                }

                // 执行软触发
                bool result = _optVisionService.ExecuteSoftwareTrigger();
                if (result)
                {
                    _logger.LogInfo("软触发拍照执行成功");
                }
                else
                {
                    _logger.LogError("软触发拍照执行失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"执行软触发拍照失败: {ex.Message}");
            }
        });

        /// <summary>
        /// 自动定时触发拍照
        /// </summary>
        public IRelayCommand TriggerMoreTimes => new RelayCommand(() =>
        {
            try
            {
                if (!_isAutoTriggerRunning)
                {
                    // 开始自动触发
                    StartAutoTrigger();
                }
                else
                {
                    // 停止自动触发
                    StopAutoTrigger();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"自动定时触发操作失败: {ex.Message}");
            }
        });

        /// <summary>
        /// 开始自动触发
        /// </summary>
        private void StartAutoTrigger()
        {
            // 检查相机连接状态
            if (!_optVisionService.IsConnect)
            {
                _logger.LogError("OPT相机未连接，无法开始自动触发");
                return;
            }

            // 解析时间间隔
            if (!int.TryParse(_timeSpan, out int interval) || interval <= 0)
            {
                _logger.LogError($"时间间隔设置无效: {_timeSpan}，请输入正整数（毫秒）");
                return;
            }

            _logger.LogInfo($"开始自动定时触发，间隔: {interval}ms");
            
            IsAutoTriggerRunning = true;
            _cancellationTokenSource = new CancellationTokenSource();
            
            // 使用Timer实现定时触发
            _autoTriggerTimer = new Timer(AutoTriggerCallback, null, 0, interval);
        }

        /// <summary>
        /// 停止自动触发
        /// </summary>
        private void StopAutoTrigger()
        {
            _logger.LogInfo("停止自动定时触发");
            
            IsAutoTriggerRunning = false;
            
            // 停止定时器
            _autoTriggerTimer?.Dispose();
            _autoTriggerTimer = null;
            
            // 取消异步任务
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }

        /// <summary>
        /// 自动触发回调
        /// </summary>
        private void AutoTriggerCallback(object state)
        {
            try
            {
                // 检查是否应该继续触发
                if (!_isAutoTriggerRunning || _cancellationTokenSource?.Token.IsCancellationRequested == true)
                {
                    return;
                }

                // 检查相机连接状态
                if (!_optVisionService.IsConnect)
                {
                    _logger.LogError("相机连接断开，停止自动触发");
                    App.Current.Dispatcher.Invoke(() => StopAutoTrigger());
                    return;
                }

                // 执行软触发
                bool result = _optVisionService.ExecuteSoftwareTrigger();
                if (result)
                {
                    _logger.LogInfo("自动触发拍照成功");
                }
                else
                {
                    _logger.LogError("自动触发拍照失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"自动触发回调异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 资源清理
        /// </summary>
        public void Dispose()
        {
            StopAutoTrigger();
        }
    }
} 