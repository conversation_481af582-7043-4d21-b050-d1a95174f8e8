﻿<Window x:Class="IPM.Vision.Views.TestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:IPM.Vision.Views"
        mc:Ignorable="d"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        DataContext="{Binding TestWindowViewModel, Source={StaticResource Locator}}"
        Title="PLC触发模拟测试" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <TextBlock Text="OPT相机触发测试控制面板" 
                       Foreground="White" 
                       FontSize="16" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center"/>
        </Border>
        
        <!-- 主内容区域 -->
        <hc:UniformSpacingPanel Grid.Row="1" Orientation="Vertical" Spacing="15" Margin="20">
            
            <!-- 相机设置区域 -->
            <GroupBox Header="相机设置">
                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                    <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                        <Button Content="设置软触发模式" 
                                Command="{Binding SetCameraModeCommand}"
                                Height="35"
                                Background="#3498db"
                                Foreground="White"
                                FontWeight="Bold"/>
                        <Button Content="取消软触发模式" 
                                Command="{Binding CancelTriggerModeCommand}"
                                Height="35"
                                Background="#95a5a6"
                                Foreground="White"
                                FontWeight="Bold"/>
                    </hc:UniformSpacingPanel>
                    <TextBlock Text="设置软触发模式：将相机设置为软触发模式，用于单次或定时触发拍照" 
                               Foreground="Gray" 
                               FontSize="12"/>
                    <TextBlock Text="取消软触发模式：关闭触发模式，恢复连续采集实时画面显示" 
                               Foreground="Gray" 
                               FontSize="12"/>
                </hc:UniformSpacingPanel>
            </GroupBox>
            
            <!-- 单次触发区域 -->
            <GroupBox Header="单次触发">
                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                    <Button Content="单次触发拍照" 
                            Command="{Binding TriggerOneTimes}"
                            Height="35"
                            Background="#27ae60"
                            Foreground="White"
                            FontWeight="Bold"/>
                    <TextBlock Text="执行一次软触发拍照，拍照结果将通过现有的帧回调机制处理" 
                               Foreground="Gray" 
                               FontSize="12"/>
                </hc:UniformSpacingPanel>
            </GroupBox>
            
            <!-- 自动触发区域 -->
            <GroupBox Header="自动定时触发">
                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="触发间隔(毫秒):" 
                                   VerticalAlignment="Center" 
                                   Margin="0,0,10,0"/>
                        <TextBox Text="{Binding TimeSpan}" 
                                 Width="100" 
                                 Height="25"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                    
                    <Button Height="35" FontWeight="Bold">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsAutoTriggerRunning}" Value="False">
                                        <Setter Property="Content" Value="开始自动触发"/>
                                        <Setter Property="Background" Value="#e74c3c"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsAutoTriggerRunning}" Value="True">
                                        <Setter Property="Content" Value="停止自动触发"/>
                                        <Setter Property="Background" Value="#e67e22"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <Button.Command>
                            <Binding Path="TriggerMoreTimes"/>
                        </Button.Command>
                    </Button>
                    
                    <!-- 状态指示器 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Ellipse Width="12" Height="12" VerticalAlignment="Center" Margin="0,0,5,0">
                            <Ellipse.Style>
                                <Style TargetType="Ellipse">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsAutoTriggerRunning}" Value="False">
                                            <Setter Property="Fill" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsAutoTriggerRunning}" Value="True">
                                            <Setter Property="Fill" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>
                        <TextBlock VerticalAlignment="Center" FontSize="12">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsAutoTriggerRunning}" Value="False">
                                            <Setter Property="Text" Value="自动触发已停止"/>
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsAutoTriggerRunning}" Value="True">
                                            <Setter Property="Text" Value="自动触发运行中"/>
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                    
                    <TextBlock Text="输入时间间隔（毫秒）后点击开始，系统将按设定间隔自动触发拍照" 
                               Foreground="Gray" 
                               FontSize="12"/>
                </hc:UniformSpacingPanel>
            </GroupBox>
            
            <!-- 使用说明 -->
            <GroupBox Header="使用说明">
                <StackPanel Margin="10">
                    <TextBlock Text="1. 确保OPT相机已连接并处于就绪状态" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="2. 点击'设置软触发模式'配置相机为软触发模式" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="3. 使用'单次触发拍照'进行测试确认" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="4. 设置合适的时间间隔后使用'自动定时触发'功能" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="5. 拍照结果将通过系统现有的图像处理流程处理" FontSize="12" Margin="0,2"/>
                </StackPanel>
            </GroupBox>
        </hc:UniformSpacingPanel>
    </Grid>
</Window>
