using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class ProcessParaService : BaseService<ProcessModel>, IProcessParaService
    {
        private readonly EquipmentService _equipmentService;
        private readonly MainCameraParaService _mainCameraParaService;
        private readonly LightParamService _lightParamService;
        public ProcessParaService(IBaseRepository<ProcessModel> baseRepository, IEquipmentService equipmentService, IMainCameraParaService mainCameraParaService, ILightParamService lightParamService) : base(baseRepository)
        {
            _equipmentService = (EquipmentService)equipmentService;
            _mainCameraParaService = (MainCameraParaService)mainCameraParaService;
            _lightParamService = (LightParamService)lightParamService;
        }

        public async Task<ObservableCollection<ObservableProcessModel>> GetProcessDataAsync(List<string> processIds)
        {
            ObservableCollection<ObservableProcessModel> result = new ObservableCollection<ObservableProcessModel>();
            if (processIds.Count > 0)
            {
                foreach (string processId in processIds)
                {
                    if (!string.IsNullOrEmpty(processId))
                    {
                        var temp = await this.getFirstOrDefault(item => item.Id == processId);
                        if (temp == null) break;
                        var temp2 = temp.MapTo<ProcessModel, ObservableProcessModel>();
                        if (!string.IsNullOrEmpty(temp2.MainCameraId))
                        {
                            var cameraPara = await _mainCameraParaService.getFirstOrDefault(item => item.Id == temp2.MainCameraId);
                            temp2.MainCameraPara = cameraPara.MapTo<MainCameraParamModel, ObservableMainCameraParaModel>();
                        }
                        if (!string.IsNullOrEmpty(temp2.LightParamId))
                        {
                            var lightPara = await _lightParamService.getFirstOrDefault(item => item.Id == temp2.LightParamId);
                            temp2.LightModel = lightPara.MapTo<LightParamModel, ObservableLightModel>();
                        }
                        if (!string.IsNullOrEmpty(temp2.EquipmentParaId))
                        {
                            var equipmentPara = await _equipmentService.getFirstOrDefault(item => item.Id == temp2.EquipmentParaId);
                            temp2.EquipmentPara = equipmentPara.MapTo<EquipmentModel, ObservableEquipmentModel>();
                        }
                        result.Add(temp2);
                    }
                    
                }
            }
            // **修复步骤排序问题：确保Mark点识别步骤始终在第一位，然后按ProcessNumber排序**
            var tempList = result.OrderBy(x => x.ProcessType == Common.ProcessTypeEnum.POINT ? 0 : 1)
                                 .ThenBy(x => x.ProcessNumber)
                                 .ToList();

            // **调试信息：输出排序后的步骤顺序**
            System.Diagnostics.Debug.WriteLine("ProcessParaService - 步骤排序结果:");
            for (int i = 0; i < tempList.Count; i++)
            {
                var process = tempList[i];
                System.Diagnostics.Debug.WriteLine($"  索引{i}: {process.ParamName} (ProcessNumber: {process.ProcessNumber}, ProcessType: {process.ProcessType})");
            }

            return new ObservableCollection<ObservableProcessModel>(tempList);
        }
    }
}
