using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class ProductChangedDialogViewModel : ViewModelBase, IDialogResultable<bool>
    {
        private bool _result = false;
        private readonly ObservableGlobalState _globalState;
        private readonly ProductParaService _productService;
        private readonly IProcessParaService _processParaService;
        private readonly ICompInfoService _compInfoService;
        private readonly EquipmentService _equipmentService;

        private ObservableCollection<ObservableProductModel> _dataList;
        private ObservableProductModel _currentProduct;
        private int _pinsPerProcess = 1;

        public ProductChangedDialogViewModel(ObservableGlobalState globalState, IProductParaService productParaService,
            IProcessParaService processParaService, ICompInfoService compInfoService, EquipmentService equipmentService)
        {
            _globalState = globalState;
            _productService = (ProductParaService)productParaService;
            _processParaService = processParaService;
            _compInfoService = compInfoService;
            _equipmentService = equipmentService;
        }

        public ObservableCollection<ObservableProductModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }

        public ObservableProductModel CurrentProduct
        {
            get => _currentProduct;
            set => SetProperty(ref _currentProduct, value);
        }

        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        /// <summary>
        /// 每个process包含的pin脚数量
        /// </summary>
        public int PinsPerProcess
        {
            get => _pinsPerProcess;
            set => SetProperty(ref _pinsPerProcess, value);
        }
        public Action CloseAction { get; set; }

        public IRelayCommand LoadCommand => new RelayCommand(async () =>
        {
            var temp = await _productService.getAll();
            DataList = temp.MapTo<List<ProductParamModel>, ObservableCollection<ObservableProductModel>>();
        });

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            CloseAction?.Invoke();
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            if (CurrentProduct != null)
            {
                // 查询产品对应的步骤和CompInfo数据，计算pin脚信息
                await LoadProductDataAsync();

                // 确保pin脚数据已经计算完成后再设置当前产品
                _globalState.CurrentProduct = CurrentProduct;

                // 通知其他ViewModel pin数据已更新
                System.Diagnostics.Debug.WriteLine("产品切换完成，pin数据已计算并设置到GlobalState");
            }
            CloseAction?.Invoke();
        });

        /// <summary>
        /// 手动计算pin脚信息命令
        /// </summary>
        public IAsyncRelayCommand GeneratePinProcessesCommand => new AsyncRelayCommand(async () =>
        {
            if (CurrentProduct?.CompModel != null)
            {
                await AutoCalculatePinInfoForStepsAsync();

                // 输出调试信息
                LogPinDistributionInfo();
            }
        });

        /// <summary>
        /// 输出pin分配信息用于调试
        /// </summary>
        private void LogPinDistributionInfo()
        {
            if (CurrentProduct?.StepList == null) return;

            var takePictureSteps = CurrentProduct.StepList.Where(s => s.ProcessType == Common.ProcessTypeEnum.TAKEPICTURE).ToList();


            for (int i = 0; i < takePictureSteps.Count; i++)
            {
                var step = takePictureSteps[i];
                if (step.PinPositions != null && step.PinPositions.Count > 0)
                {
                    var firstPin = step.PinPositions.First();
                    var lastPin = step.PinPositions.Last();

                }
            }
        }

        /// <summary>
        /// 加载当前产品的步骤数据和CompInfo数据
        /// </summary>
        private async Task LoadProductDataAsync()
        {
            try
            {
                if (CurrentProduct == null) return;

                // 1. 加载步骤数据
                if (CurrentProduct.ProcessIds != null && CurrentProduct.ProcessIds.Count > 0)
                {
                    // 将ProcessIds转换为List<string>
                    var processIdList = CurrentProduct.ProcessIds.ToList();

                    // 使用ProcessParaService查询步骤数据
                    if (_processParaService is ProcessParaService processService)
                    {
                        var stepList = await processService.GetProcessDataAsync(processIdList);

                        // 将步骤数据设置到产品模型中
                        CurrentProduct.StepList = stepList;
                    }
                }

                // 2. 加载CompInfo数据
                if (!string.IsNullOrEmpty(CurrentProduct.CompInfoId))
                {
                    var compInfoData = await _compInfoService.getFirstOrDefault(item => item.Id == CurrentProduct.CompInfoId);
                    if (compInfoData != null)
                    {
                        CurrentProduct.CompModel = compInfoData.MapTo<CompInfoModel, ObservableCompModel>();

                        // 3. 自动计算pin脚信息并添加到现有步骤中
                        await AutoCalculatePinInfoForStepsAsync();
                    }
                }

            }
            catch (Exception ex)
            {
                // 可以在这里添加日志记录或错误处理
                System.Diagnostics.Debug.WriteLine($"加载产品数据时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 自动计算pin脚信息并添加到现有步骤中
        /// </summary>
        private async Task AutoCalculatePinInfoForStepsAsync()
        {
            try
            {
                if (CurrentProduct?.CompModel == null) return;

                // 生成pin脚坐标
                var pinCoordinates = GeneratePinCoordinatesFromCompModel(CurrentProduct.CompModel);
                if (pinCoordinates.Count == 0)
                {
                    return;
                }

                // 检查是否有现有的步骤
                if (CurrentProduct.StepList == null || CurrentProduct.StepList.Count == 0)
                {
                    return;
                }

                // 将pin脚添加到现有步骤中
                await AddPinCoordinatesToExistingStepsAsync(pinCoordinates);

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动计算pin脚信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将pin脚坐标添加到现有步骤中，按边或按数量分组分配
        /// </summary>
        private async Task AddPinCoordinatesToExistingStepsAsync(List<ObservablePinPosition> pinCoordinates)
        {
            try
            {
                if (CurrentProduct.StepList == null || pinCoordinates.Count == 0) return;

                // 获取所有拍照步骤
                var takePictureSteps = CurrentProduct.StepList.Where(s => s.ProcessType == Common.ProcessTypeEnum.TAKEPICTURE).ToList();

                // 如果没有足够的步骤，则只使用第一个步骤
                if (takePictureSteps.Count == 0) return;

                // 根据PinsPerProcess设置分配策略
                if (PinsPerProcess <= 1)
                {
                    // 按边分配：每个步骤处理一条边的所有pin
                    await AssignPinsByEdgeToStepsAsync(pinCoordinates, takePictureSteps);
                }
                else
                {
                    // 按数量分配：每个步骤处理指定数量的pin（按顺序分配）
                    await AssignPinsByCountToStepsAsync(pinCoordinates, takePictureSteps);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加pin脚到现有步骤失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 按边将pin分配到步骤，每个步骤处理一条边的所有pin
        /// </summary>
        private Task AssignPinsByEdgeToStepsAsync(List<ObservablePinPosition> pinCoordinates, List<ObservableProcessModel> takePictureSteps)
        {
            // 按边分组pin坐标
            var pinsByEdge = pinCoordinates.GroupBy(p => p.EdgeNumber).OrderBy(g => g.Key).ToList();

            for (int i = 0; i < pinsByEdge.Count && i < takePictureSteps.Count; i++)
            {
                var edgeGroup = pinsByEdge[i];
                var step = takePictureSteps[i];

                // 清空现有pin数据
                if (step.PinPositions == null)
                {
                    step.PinPositions = new ObservableCollection<ObservablePinPosition>();
                }
                else
                {
                    step.PinPositions.Clear();
                }

                // 只添加实际的pin坐标到DataGrid显示（不包含加速减速点）
                foreach (var pin in edgeGroup)
                {
                    step.PinPositions.Add(pin);
                }

                // 更新步骤信息
                step.PinCount = edgeGroup.Count();
                step.PinStartIndex = edgeGroup.First().Index;

                // 更新步骤名称
                var edgeName = GetEdgeName(edgeGroup.Key);
                step.ParamName = $"{step.ParamName.Split('-')[0]}-{edgeName}({edgeGroup.Count()})";

                System.Diagnostics.Debug.WriteLine($"步骤 {i + 1}: {edgeName} - Pin {edgeGroup.First().Index} 到 {edgeGroup.Last().Index} (共{edgeGroup.Count()}个)");
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 按数量将pin分配到步骤，每个步骤处理指定数量的pin（按顺序分配）
        /// </summary>
        private Task AssignPinsByCountToStepsAsync(List<ObservablePinPosition> pinCoordinates, List<ObservableProcessModel> takePictureSteps)
        {
            int stepIndex = 0;
            int pinIndex = 0;

            while (pinIndex < pinCoordinates.Count && stepIndex < takePictureSteps.Count)
            {
                var step = takePictureSteps[stepIndex];

                // 清空现有pin数据
                if (step.PinPositions == null)
                {
                    step.PinPositions = new ObservableCollection<ObservablePinPosition>();
                }
                else
                {
                    step.PinPositions.Clear();
                }

                // 添加指定数量的pin到当前步骤
                int pinsToAdd = Math.Min(PinsPerProcess, pinCoordinates.Count - pinIndex);
                var startPinIndex = pinCoordinates[pinIndex].Index;
                var endPinIndex = pinCoordinates[pinIndex + pinsToAdd - 1].Index;

                // 只添加实际的pin坐标到DataGrid显示（不包含加速减速点）
                for (int i = 0; i < pinsToAdd; i++)
                {
                    step.PinPositions.Add(pinCoordinates[pinIndex + i]);
                }

                // 更新步骤信息
                step.PinCount = pinsToAdd;
                step.PinStartIndex = startPinIndex;

                // 更新步骤名称，显示pin范围
                step.ParamName = $"{step.ParamName.Split('-')[0]}-PIN({startPinIndex}-{endPinIndex})";

                System.Diagnostics.Debug.WriteLine($"步骤 {stepIndex + 1}: Pin {startPinIndex} 到 {endPinIndex} (共{pinsToAdd}个)");

                pinIndex += pinsToAdd;
                stepIndex++;
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 获取边的名称
        /// </summary>
        private string GetEdgeName(int edgeNumber)
        {
            switch (edgeNumber)
            {
                case 1: return "上边";
                case 2: return "右边";
                case 3: return "下边";
                case 4: return "左边";
                default: return $"边{edgeNumber}";
            }
        }





        // 根据起点位置和方向计算pin脚坐标
        private List<ObservablePinPosition> GeneratePinCoordinatesFromCompModel(ObservableCompModel compModel)
        {
            var pinPositions = new List<ObservablePinPosition>();

            try
            {
                // 根据起点位置和方向获取边的处理顺序
                var edgeSequence = GetEdgeSequence(compModel);

                int pinIndex = 1;

                // 按照正确的顺序处理各边
                foreach (int edge in edgeSequence)
                {
                    var edgeData = GetEdgeDataComplete(compModel, edge);
                    if (edgeData.pin <= 0) continue;

                    // 计算当前边的pin脚坐标
                    for (int i = 0; i < edgeData.pin; i++)
                    {
                        var direction = GetEdgeDirectionSimple(edge);
                        float pinX = edgeData.x + direction.deltaX * i * edgeData.step;
                        float pinY = edgeData.y + direction.deltaY * i * edgeData.step;

                        pinPositions.Add(new ObservablePinPosition
                        {
                            Index = pinIndex,
                            X = pinX,
                            Y = pinY,
                            Z = edgeData.z,
                            R = edgeData.r,
                            T = edgeData.t,
                            EdgeNumber = edge,
                            PinIndexInEdge = i,
                            EdgeName = GetEdgeName(edge)
                        });

                        pinIndex++;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成pin脚坐标失败: {ex.Message}");
            }

            return pinPositions;
        }

        /// <summary>
        /// 根据起点位置和方向获取边的拍摄顺序
        /// </summary>
        /// <param name="compModel">组件模型</param>
        /// <returns>边的顺序数组</returns>
        private int[] GetEdgeSequence(ObservableCompModel compModel)
        {
            // 基础顺序：左上角开始，逆时针方向 [1,2,3,4] 对应 [上边,右边,下边,左边]
            int[] baseSequence = { 1, 2, 3, 4 };

            // 根据起点位置调整起始边
            int startOffset = (int)compModel.PinStartPosition;

            // 根据方向决定是否反转
            bool isClockwise = compModel.PinDirection == Common.PinDirectionEnum.Clockwise;

            // 创建新的序列
            int[] sequence = new int[4];
            for (int i = 0; i < 4; i++)
            {
                int index = isClockwise ?
                    (startOffset - i + 4) % 4 : // 顺时针：向前
                    (startOffset + i) % 4;      // 逆时针：向后
                sequence[i] = baseSequence[index];
            }

            return sequence;
        }

        private (float x, float y, int pin, float step) GetEdgeDataSimple(ObservableCompModel compModel, int edge)
        {
            switch (edge)
            {
                case 1: return (compModel.X1, compModel.Y1, compModel.Pin1, compModel.Step1);
                case 2: return (compModel.X2, compModel.Y2, compModel.Pin2, compModel.Step2);
                case 3: return (compModel.X3, compModel.Y3, compModel.Pin3, compModel.Step3);
                case 4: return (compModel.X4, compModel.Y4, compModel.Pin4, compModel.Step4);
                default: return (0, 0, 0, 0);
            }
        }

        /// <summary>
        /// 获取边的完整数据，包括Z、R、T坐标
        /// </summary>
        private (float x, float y, float z, float r, float t, int pin, float step) GetEdgeDataComplete(ObservableCompModel compModel, int edge)
        {
            switch (edge)
            {
                case 1: return (compModel.X1, compModel.Y1, compModel.Z1, compModel.R1, compModel.T1, compModel.Pin1, compModel.Step1);
                case 2: return (compModel.X2, compModel.Y2, compModel.Z2, compModel.R2, compModel.T2, compModel.Pin2, compModel.Step2);
                case 3: return (compModel.X3, compModel.Y3, compModel.Z3, compModel.R3, compModel.T3, compModel.Pin3, compModel.Step3);
                case 4: return (compModel.X4, compModel.Y4, compModel.Z4, compModel.R4, compModel.T4, compModel.Pin4, compModel.Step4);
                default: return (0, 0, 0, 0, 0, 0, 0);
            }
        }

        private (int deltaX, int deltaY) GetEdgeDirectionSimple(int edge)
        {
            switch (edge)
            {
                case 1: return (0, 1);   // 上边：Y轴正方向
                case 2: return (1, 0);   // 右边：X轴正方向
                case 3: return (0, -1);  // 下边：Y轴负方向
                case 4: return (-1, 0);  // 左边：X轴负方向
                default: return (0, 0);
            }
        }


    }
}

